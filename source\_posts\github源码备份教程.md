---
title: github源码备份教程
date: 2025-01-27 10:00:00
tags:
  - Git
  - GitHub
  - 备份
  - 教程
categories:
  - 技术教程
# 封面图片路径 (可选)
cover: imgs/covers/tech-cover-1.svg
# 可选封面模板:
# cover: imgs/covers/ai-cover.svg
# cover: imgs/covers/tech-cover-2.svg
# cover: imgs/covers/blog-cover.svg
description: 详细记录GitHub源码备份的完整流程，包括仓库配置、文件添加、提交和推送等关键步骤
---

<style>
.terminal-block {
    background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
    border-radius: 8px;
    margin: 15px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    font-family: 'Courier New', monospace;
}

.terminal-header {
    background: linear-gradient(90deg, #3a3a3a 0%, #4a4a4a 100%);
    padding: 8px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #555;
}

.terminal-title {
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
}

.terminal-buttons {
    display: flex;
    gap: 6px;
}

.terminal-btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
}

.terminal-btn.close { background: #ff5f57; }
.terminal-btn.minimize { background: #ffbd2e; }
.terminal-btn.maximize { background: #28ca42; }

.terminal-content {
    padding: 15px;
    color: #00ff00;
    font-size: 14px;
    position: relative;
    background: #1e1e1e;
}

.terminal-prompt {
    color: #00ff00;
    margin-right: 8px;
}

.copy-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: #007acc;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: background 0.3s;
}

.copy-btn:hover {
    background: #005a9e;
}
</style>

<script>
function copyTerminalContent(text, button) {
    navigator.clipboard.writeText(text).then(function() {
        const originalText = button.textContent;
        button.textContent = '已复制';
        button.style.background = '#28a745';
        setTimeout(function() {
            button.textContent = originalText;
            button.style.background = '#007acc';
        }, 2000);
    }).catch(function(err) {
        console.error('复制失败: ', err);
    });
}
</script>

# GitHub源码备份教程

在开发过程中，定期备份源码到GitHub是一个重要的习惯。本文将详细记录完整的提交流程，方便下次操作时参考。从github下载的源码必须使用npm install安装依赖

## 1. 检查远程仓库状态

首先检查当前配置的远程仓库地址：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">检查远程仓库状态</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>git remote -v
        <button class="copy-btn" onclick="copyTerminalContent('git remote -v', this)">复制</button>
    </div>
</div>

这个命令会显示当前配置的所有远程仓库及其URL。如果显示的不是目标仓库地址，需要进行更新。

## 2. 更新远程仓库地址

删除现在的仓库地址命令：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">删除远程仓库地址</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>git remote remove origin
        <button class="copy-btn" onclick="copyTerminalContent('git remote remove origin', this)">复制</button>
    </div>
</div>

如果需要更改远程仓库地址，使用以下命令：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">设置远程仓库地址</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>git remote set-url origin https://github.com/qianxiu203/仓库名称.git
        <button class="copy-btn" onclick="copyTerminalContent('git remote set-url origin https://github.com/qianxiu203/仓库名称.git', this)">复制</button>
    </div>
</div>

这个命令会将origin远程仓库的URL更新为指定的GitHub仓库地址。

## 3. 添加文件到暂存区

将所有文件添加到Git暂存区：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">添加文件到暂存区</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>git add . --force
        <button class="copy-btn" onclick="copyTerminalContent('git add . --force', this)">复制</button>
    </div>
</div>

- `.` 表示添加当前目录下的所有文件
- `--force` 参数强制添加文件，即使某些文件被.gitignore忽略

## 4. 检查仓库状态

在提交前，检查当前仓库的状态：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">检查仓库状态</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>git status
        <button class="copy-btn" onclick="copyTerminalContent('git status', this)">复制</button>
    </div>
</div>

这个命令会显示：
- 已暂存的文件（绿色）
- 未暂存的修改（红色）
- 未跟踪的文件

确认所有需要提交的文件都已正确添加到暂存区。

## 5. 提交更改

将暂存区的文件提交到本地仓库：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">提交更改</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>git commit -m "初始化Hexo博客源码仓库"
        <button class="copy-btn" onclick="copyTerminalContent('git commit -m \"初始化Hexo博客源码仓库\"', this)">复制</button>
    </div>
</div>

- `-m` 参数后跟提交信息
- 提交信息应该简洁明了地描述本次提交的内容

## 6. 推送到远程仓库

最后，将本地提交推送到GitHub远程仓库：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">推送到远程仓库</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>git push -u origin master
        <button class="copy-btn" onclick="copyTerminalContent('git push -u origin master', this)">复制</button>
    </div>
</div>

- `-u` 参数设置上游分支，后续可以直接使用 `git push`
- `origin` 是远程仓库的名称
- `master` 是要推送的分支名称

## 提交成功确认

当看到类似以下输出时，表示提交成功：

```
Enumerating objects: X, done.
Counting objects: 100% (X/X), done.
Delta compression using up to X threads
Compressing objects: 100% (X/X), done.
Writing objects: 100% (X/X), X.XX KiB | X.XX MiB/s, done.
Total X (delta X), reused X (delta X), pack-reused 0
To https://github.com/qianxiu203/hexo-blog-source.git
 * [new branch]      master -> master
Branch 'master' set up to track remote branch 'master' from 'origin'.
```

## 常见问题解决

### 1. 推送被拒绝
如果遇到推送被拒绝的情况，可能是因为远程仓库有新的提交。可以先拉取远程更改：

<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">拉取远程更改</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>git pull origin master
        <button class="copy-btn" onclick="copyTerminalContent('git pull origin master', this)">复制</button>
    </div>
</div>

### 2. 认证问题
如果遇到认证问题，确保：
- GitHub账户有仓库的写入权限
- 使用正确的用户名和密码/token
- 配置了SSH密钥（如果使用SSH URL）

### 3. 分支问题
如果默认分支不是master，需要相应调整命令中的分支名称。

## 总结

完整的GitHub源码备份流程包括：
1. 检查远程仓库配置
2. 更新仓库地址（如需要）
3. 添加文件到暂存区
4. 检查状态
5. 提交更改
6. 推送到远程仓库

按照这个流程操作，可以确保源码安全地备份到GitHub仓库中。建议定期执行这个流程，保持代码的及时备份。
---

## ⚠️ 免责声明

本文档仅供学习和研究使用，请勿用于任何非法活动。使用者需遵守当地法律法规，作者不承担任何责任。请在24小时内删除相关内容。
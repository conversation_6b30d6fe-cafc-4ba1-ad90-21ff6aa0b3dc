<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时钟和主题优化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-button {
            margin: 5px;
            padding: 8px 16px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #005a87;
        }
        .test-output {
            margin: 10px 0;
            padding: 10px;
            background: #fff;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
        .status-info {
            margin: 10px 0;
            padding: 10px;
            background: #e7f3ff;
            border-left: 4px solid #007cba;
        }
    </style>
</head>
<body>
    <h1>🔧 时钟和主题优化测试页面</h1>
    
    <div class="status-info">
        <h3>📊 当前状态</h3>
        <p><strong>当前时间:</strong> <span id="current-time">--</span></p>
        <p><strong>当前主题:</strong> <span id="current-theme">--</span></p>
        <p><strong>位置信息:</strong> <span id="current-location">获取中...</span></p>
        <p><strong>天气信息:</strong> <span id="current-weather">获取中...</span></p>
    </div>

    <div class="test-section">
        <h3>🌍 位置获取测试</h3>
        <p>测试浏览器地理位置权限申请和位置获取功能</p>
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 4px;">
            <strong>⚠️ 重要提示：</strong>
            <ul style="margin: 5px 0; padding-left: 20px;">
                <li>地理位置功能需要HTTPS环境或localhost</li>
                <li>首次使用需要用户主动点击按钮申请权限</li>
                <li>请允许浏览器访问您的位置以获得准确的天气信息</li>
            </ul>
        </div>
        <button class="test-button" onclick="requestLocationPermission()" style="background: #28a745; font-weight: bold;">🎯 申请位置权限</button>
        <button class="test-button" onclick="testLocationUpdate()">强制更新位置</button>
        <button class="test-button" onclick="testBrowserLocation()">测试浏览器定位</button>
        <button class="test-button" onclick="testReverseGeocode()">测试坐标转换</button>
        <button class="test-button" onclick="clearLocationData()">清除位置数据</button>
        <div id="location-output" class="test-output"></div>
    </div>

    <div class="test-section">
        <h3>🎨 主题切换测试</h3>
        <p>测试2分钟自动恢复功能</p>
        <button class="test-button" onclick="testManualThemeSwitch()">手动切换主题</button>
        <button class="test-button" onclick="testThemeStatus()">查看主题状态</button>
        <button class="test-button" onclick="testForceAutoSwitch()">强制自动切换</button>
        <button class="test-button" onclick="clearManualOverride()">清除手动标记</button>
        <div id="theme-output" class="test-output"></div>
    </div>

    <div class="test-section">
        <h3>⏱️ 时间测试</h3>
        <p>测试2分钟倒计时</p>
        <button class="test-button" onclick="startCountdown()">开始2分钟倒计时</button>
        <button class="test-button" onclick="stopCountdown()">停止倒计时</button>
        <div id="countdown-display" style="font-size: 18px; font-weight: bold; margin: 10px 0;">--:--</div>
        <div id="countdown-output" class="test-output"></div>
    </div>

    <script>
        let countdownInterval = null;
        
        // 更新当前状态显示
        function updateStatus() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString('zh-CN');
            
            const theme = document.documentElement.getAttribute('data-theme') || 'light';
            document.getElementById('current-theme').textContent = theme === 'dark' ? '深色模式' : '浅色模式';
            
            // 获取位置信息
            if (window.weatherManager && window.weatherManager.city) {
                document.getElementById('current-location').textContent = window.weatherManager.city;
            }
            
            // 获取天气信息
            if (window.weatherManager && window.weatherManager.weatherData) {
                const weather = window.weatherManager.weatherData;
                document.getElementById('current-weather').textContent = 
                    `${weather.weather || '--'} ${weather.temperature || '--'}°C`;
            }
        }

        // 日志输出函数
        function logToOutput(outputId, message) {
            const output = document.getElementById(outputId);
            const time = new Date().toLocaleTimeString('zh-CN');
            output.innerHTML += `[${time}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
        }

        // 位置测试函数
        function requestLocationPermission() {
            logToOutput('location-output', '🎯 用户主动申请位置权限...');

            // 检查环境
            const isHttps = location.protocol === 'https:';
            const isLocalhost = location.hostname === 'localhost' || location.hostname === '127.0.0.1';

            if (!isHttps && !isLocalhost) {
                logToOutput('location-output', '❌ 当前环境不支持地理位置API');
                logToOutput('location-output', `当前协议: ${location.protocol}, 主机: ${location.hostname}`);
                logToOutput('location-output', '💡 请使用HTTPS或localhost访问');
                return;
            }

            if (window.weatherManager) {
                window.weatherManager.triggerLocationRequest().then(result => {
                    if (result) {
                        logToOutput('location-output', `✅ 位置权限申请成功: ${result}`);
                        logToOutput('location-output', '🌤️ 正在获取天气信息...');
                    } else {
                        logToOutput('location-output', '❌ 位置权限申请失败');
                        logToOutput('location-output', '💡 请检查浏览器是否允许位置访问');
                    }
                });
            } else {
                logToOutput('location-output', '❌ 天气管理器未找到');
            }
        }

        function testLocationUpdate() {
            logToOutput('location-output', '🔄 开始强制位置更新...');
            if (window.weatherManager) {
                window.weatherManager.backgroundLocationUpdate();
                logToOutput('location-output', '✅ 位置更新已触发');
            } else {
                logToOutput('location-output', '❌ 天气管理器未找到');
            }
        }

        function testBrowserLocation() {
            logToOutput('location-output', '📱 测试浏览器地理位置...');
            if (window.weatherManager) {
                window.weatherManager.requestBrowserLocation().then(result => {
                    if (result) {
                        logToOutput('location-output', `✅ 浏览器定位成功: ${result}`);
                    } else {
                        logToOutput('location-output', '❌ 浏览器定位失败（可能被拒绝或超时）');
                    }
                });
            } else {
                logToOutput('location-output', '❌ 天气管理器未找到');
            }
        }

        function testReverseGeocode() {
            logToOutput('location-output', '🌍 测试坐标转换（使用北京坐标）...');
            if (window.weatherManager) {
                // 使用北京的坐标进行测试
                const lat = 39.9042;
                const lng = 116.4074;
                window.weatherManager.reverseGeocode(lat, lng).then(result => {
                    if (result) {
                        logToOutput('location-output', `✅ 坐标转换成功: ${result}`);
                    } else {
                        logToOutput('location-output', '❌ 坐标转换失败');
                    }
                });
            } else {
                logToOutput('location-output', '❌ 天气管理器未找到');
            }
        }

        function clearLocationData() {
            logToOutput('location-output', '🗑️ 清除位置数据...');
            localStorage.removeItem('shared-city-data');
            localStorage.removeItem('shared-weather-data');
            logToOutput('location-output', '✅ 位置数据已清除');
        }

        // 主题测试函数
        function testManualThemeSwitch() {
            logToOutput('theme-output', '🎨 执行手动主题切换...');
            if (window.autoThemeSwitcher) {
                window.autoThemeSwitcher.toggleTheme();
                logToOutput('theme-output', '✅ 手动切换完成，2分钟后将自动恢复');
            } else {
                logToOutput('theme-output', '❌ 自动主题切换器未找到');
            }
        }

        function testThemeStatus() {
            logToOutput('theme-output', '📊 获取主题状态...');
            if (window.autoThemeSwitcher) {
                const status = window.autoThemeSwitcher.getStatus();
                logToOutput('theme-output', `当前主题: ${status.currentTheme}`);
                logToOutput('theme-output', `手动模式: ${status.isManualOverride ? '是' : '否'}`);
                logToOutput('theme-output', `自动切换: ${status.enabled ? '启用' : '禁用'}`);
                
                if (status.isManualOverride) {
                    const manualTime = parseInt(localStorage.getItem('theme_manual_time') || '0');
                    const elapsed = Date.now() - manualTime;
                    const remaining = Math.max(0, 2 * 60 * 1000 - elapsed);
                    const remainingSeconds = Math.ceil(remaining / 1000);
                    logToOutput('theme-output', `剩余时间: ${remainingSeconds}秒`);
                }
            } else {
                logToOutput('theme-output', '❌ 自动主题切换器未找到');
            }
        }

        function testForceAutoSwitch() {
            logToOutput('theme-output', '⏰ 强制自动切换...');
            if (window.autoThemeSwitcher) {
                window.autoThemeSwitcher.autoSwitchThemeByTime();
                logToOutput('theme-output', '✅ 自动切换检查完成');
            } else {
                logToOutput('theme-output', '❌ 自动主题切换器未找到');
            }
        }

        function clearManualOverride() {
            logToOutput('theme-output', '🔄 清除手动切换标记...');
            localStorage.removeItem('theme_manual_override');
            localStorage.removeItem('theme_manual_time');
            logToOutput('theme-output', '✅ 手动标记已清除');
            
            if (window.autoThemeSwitcher) {
                window.autoThemeSwitcher.updateStatusIndicator();
            }
        }

        // 倒计时测试
        function startCountdown() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }
            
            let seconds = 2 * 60; // 2分钟
            logToOutput('countdown-output', '⏰ 开始2分钟倒计时测试...');
            
            countdownInterval = setInterval(() => {
                const minutes = Math.floor(seconds / 60);
                const secs = seconds % 60;
                const display = `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                document.getElementById('countdown-display').textContent = display;
                
                if (seconds <= 0) {
                    clearInterval(countdownInterval);
                    logToOutput('countdown-output', '🎉 2分钟倒计时完成！');
                    document.getElementById('countdown-display').textContent = '完成!';
                    
                    // 检查主题是否应该自动恢复
                    if (window.autoThemeSwitcher) {
                        const isManual = window.autoThemeSwitcher.isManualOverrideActive();
                        if (!isManual) {
                            logToOutput('countdown-output', '✅ 手动模式已自动恢复');
                        } else {
                            logToOutput('countdown-output', '⚠️ 手动模式仍然活跃');
                        }
                    }
                    return;
                }
                
                seconds--;
            }, 1000);
        }

        function stopCountdown() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
                document.getElementById('countdown-display').textContent = '--:--';
                logToOutput('countdown-output', '⏹️ 倒计时已停止');
            }
        }

        // 检查环境支持
        function checkEnvironment() {
            const isHttps = location.protocol === 'https:';
            const isLocalhost = location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            const supportsGeolocation = !!navigator.geolocation;

            logToOutput('location-output', '🔍 环境检查结果:');
            logToOutput('location-output', `协议: ${location.protocol} ${isHttps ? '✅' : '❌'}`);
            logToOutput('location-output', `主机: ${location.hostname} ${isLocalhost ? '(本地)' : ''}`);
            logToOutput('location-output', `地理位置API: ${supportsGeolocation ? '✅ 支持' : '❌ 不支持'}`);

            if (isHttps || isLocalhost) {
                logToOutput('location-output', '✅ 环境支持地理位置功能');
            } else {
                logToOutput('location-output', '❌ 环境不支持地理位置功能，需要HTTPS');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            setInterval(updateStatus, 1000);

            // 检查环境
            checkEnvironment();

            // 监听主题切换事件
            document.addEventListener('themeChanged', (event) => {
                const { theme, isAutoSwitch } = event.detail;
                const switchType = isAutoSwitch ? '自动' : '手动';
                logToOutput('theme-output', `🎨 主题${switchType}切换到: ${theme === 'dark' ? '深色模式' : '浅色模式'}`);
                updateStatus();
            });

            console.log('🚀 测试页面已加载');
        });
    </script>
</body>
</html>
